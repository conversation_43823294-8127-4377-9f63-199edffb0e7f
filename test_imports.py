#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')
django.setup()

def test_imports():
    """Test all the imports"""
    print("🔍 Testing imports...")
    
    try:
        # Test existing service import
        from customer.services import CustomerMemoryService
        print("✅ CustomerMemoryService imported successfully")
        
        # Test new policy workflow services
        from customer.services import PolicyWorkflowService
        print("✅ PolicyWorkflowService imported successfully")
        
        from customer.services import TPAApiService
        print("✅ TPAApiService imported successfully")
        
        from customer.services import WorkflowConfigService
        print("✅ WorkflowConfigService imported successfully")
        
        from customer.services import WorkflowMonitoringService
        print("✅ WorkflowMonitoringService imported successfully")
        
        # Test models
        from customer.models import CustomerPolicyWorkflowCache, PolicyWorkflowAuditLog
        print("✅ New models imported successfully")
        
        # Test views
        from customer.views import CustomerPolicyListWorkflowView, CustomerPolicyDetailsWorkflowView
        print("✅ New views imported successfully")
        
        # Test exceptions
        from customer.exceptions import PolicyWorkflowError, TPAApiError, CustomerDataError
        print("✅ Exception classes imported successfully")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
