import requests
import time
import logging
from typing import Dict, Any, Optional
from django.conf import settings

logger = logging.getLogger(__name__)

class TPAApiService:
    """Service for TPA (Third Party Administrator) API integration"""
    
    BASE_URL = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2"
    
    # TPA API credentials
    USERNAME = "BVTPA"
    PASSWORD = "*d!n^+Cb@1"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 30
    
    def _make_request_with_retry(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with retry logic"""
        max_retries = 3
        retry_delay = 3  # seconds
        
        for attempt in range(max_retries):
            try:
                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()
                return response
            except requests.exceptions.RequestException as e:
                logger.warning(f"TPA API request failed (attempt {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    raise
    
    def get_bearer_token(self) -> str:
        """Get bearer token from TPA API"""
        url = f"{self.BASE_URL}/api/Token"
        
        payload = {
            "grant_type": "password",
            "username": self.USERNAME,
            "password": self.PASSWORD
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            response = self._make_request_with_retry("POST", url, data=payload, headers=headers)
            token_data = response.json()
            return token_data.get("access_token")
        except Exception as e:
            logger.error(f"Failed to get TPA bearer token: {str(e)}")
            raise Exception(f"TPA authentication failed: {str(e)}")
    
    def verify_citizen_id(self, token: str, citizen_id: str) -> Dict[str, Any]:
        """Verify citizen ID with TPA API"""
        url = f"{self.BASE_URL}/api/SearchCitizenID"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "CitizenID": citizen_id
        }
        
        try:
            response = self._make_request_with_retry("POST", url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to verify citizen ID {citizen_id}: {str(e)}")
            raise Exception(f"Citizen ID verification failed: {str(e)}")
    
    def check_registration(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Check registration status with TPA API"""
        url = f"{self.BASE_URL}/api/CheckRegister"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }
        
        try:
            response = self._make_request_with_retry("POST", url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to check registration for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Registration check failed: {str(e)}")
    
    def get_policy_list(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Get policy list from TPA API"""
        url = f"{self.BASE_URL}/api/PolicyListSocial"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }
        
        try:
            response = self._make_request_with_retry("POST", url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get policy list for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Policy list retrieval failed: {str(e)}")
    
    def get_policy_details(self, token: str, citizen_id: str, social_id: str, channel_id: str, member_code: str) -> Dict[str, Any]:
        """Get policy details from TPA API"""
        url = f"{self.BASE_URL}/api/PolicyDetailSocial"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id,
            "MemberCode": member_code
        }
        
        try:
            response = self._make_request_with_retry("POST", url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get policy details for member {member_code}: {str(e)}")
            raise Exception(f"Policy details retrieval failed: {str(e)}")
