import uuid
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User

from customer.models import Customer, CustomerPlatformIdentity, CustomerPolicyWorkflowCache, PolicyWorkflowAuditLog
from .tpa_service import TPAApiService
from customer.exceptions import PolicyWorkflowError, TPAApiError, CustomerDataError

logger = logging.getLogger(__name__)

class PolicyWorkflowService:
    """Service for executing policy workflows with caching and audit logging"""
    
    CACHE_DURATION_MINUTES = 30
    
    @classmethod
    def execute_policy_list_workflow(cls, customer_id: int, user: User) -> Dict[str, Any]:
        """Execute complete policy list workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Get customer and platform identity
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity(customer)
            
            # Check cache first
            cached_result = cls._get_cached_result(customer, 'POLICY_LIST')
            if cached_result:
                logger.info(f"Returning cached policy list for customer {customer_id}")
                return cached_result['processed_data']
            
            # Execute workflow steps
            tpa_service = TPAApiService()
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            
            # Step 1: Get bearer token
            step_start = time.time()
            token = tpa_service.get_bearer_token()
            step_time = (time.time() - step_start) * 1000
            step_results['step_1_token'] = {'success': True, 'time_ms': step_time}
            tpa_calls += 1
            tpa_time += step_time
            
            # Step 2: Verify citizen ID
            step_start = time.time()
            citizen_verification = tpa_service.verify_citizen_id(token, platform_identity.citizen_id)
            step_time = (time.time() - step_start) * 1000
            step_results['step_2_verify'] = {'success': True, 'time_ms': step_time, 'data': citizen_verification}
            tpa_calls += 1
            tpa_time += step_time
            
            # Step 3: Check registration
            step_start = time.time()
            registration_check = tpa_service.check_registration(
                token, platform_identity.citizen_id, 
                platform_identity.social_id, platform_identity.channel_id
            )
            step_time = (time.time() - step_start) * 1000
            step_results['step_3_registration'] = {'success': True, 'time_ms': step_time, 'data': registration_check}
            tpa_calls += 1
            tpa_time += step_time
            
            # Step 4: Get policy list
            step_start = time.time()
            policy_list_data = tpa_service.get_policy_list(
                token, platform_identity.citizen_id,
                platform_identity.social_id, platform_identity.channel_id
            )
            step_time = (time.time() - step_start) * 1000
            step_results['step_4_policy_list'] = {'success': True, 'time_ms': step_time, 'data': policy_list_data}
            tpa_calls += 1
            tpa_time += step_time
            
            # Process and format data
            processed_data = cls._process_policy_list_data(policy_list_data)
            
            # Cache the result
            total_time = (time.time() - start_time) * 1000
            cls._cache_result(customer, 'POLICY_LIST', None, platform_identity, 
                            policy_list_data, processed_data, execution_id, total_time)
            
            # Log audit
            cls._log_audit(customer, user, 'POLICY_LIST', execution_id, step_results, 
                          total_time, True, None, tpa_calls, tpa_time)
            
            return processed_data
            
        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}
            
            # Log failed audit
            cls._log_audit(customer, user, 'POLICY_LIST', execution_id, step_results, 
                          total_time, False, error_details, tpa_calls, tpa_time)
            
            logger.error(f"Policy list workflow failed for customer {customer_id}: {str(e)}")
            raise PolicyWorkflowError(f"Policy list workflow failed: {str(e)}")
    
    @classmethod
    def execute_policy_details_workflow(cls, customer_id: int, member_code: str, user: User) -> Dict[str, Any]:
        """Execute complete policy details workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Get customer and platform identity
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity(customer)
            
            # Check cache first
            cached_result = cls._get_cached_result(customer, 'POLICY_DETAILS', member_code)
            if cached_result:
                logger.info(f"Returning cached policy details for customer {customer_id}, member {member_code}")
                return cached_result['processed_data']
            
            # Execute workflow steps
            tpa_service = TPAApiService()
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            
            # Step 1: Get bearer token
            step_start = time.time()
            token = tpa_service.get_bearer_token()
            step_time = (time.time() - step_start) * 1000
            step_results['step_1_token'] = {'success': True, 'time_ms': step_time}
            tpa_calls += 1
            tpa_time += step_time
            
            # Step 2: Verify citizen ID
            step_start = time.time()
            citizen_verification = tpa_service.verify_citizen_id(token, platform_identity.citizen_id)
            step_time = (time.time() - step_start) * 1000
            step_results['step_2_verify'] = {'success': True, 'time_ms': step_time, 'data': citizen_verification}
            tpa_calls += 1
            tpa_time += step_time
            
            # Step 3: Get policy details
            step_start = time.time()
            policy_details_data = tpa_service.get_policy_details(
                token, platform_identity.citizen_id,
                platform_identity.social_id, platform_identity.channel_id, member_code
            )
            step_time = (time.time() - step_start) * 1000
            step_results['step_3_policy_details'] = {'success': True, 'time_ms': step_time, 'data': policy_details_data}
            tpa_calls += 1
            tpa_time += step_time
            
            # Process and format data
            processed_data = cls._process_policy_details_data(policy_details_data, member_code)
            
            # Cache the result
            total_time = (time.time() - start_time) * 1000
            cls._cache_result(customer, 'POLICY_DETAILS', member_code, platform_identity,
                            policy_details_data, processed_data, execution_id, total_time)
            
            # Log audit
            cls._log_audit(customer, user, 'POLICY_DETAILS', execution_id, step_results,
                          total_time, True, None, tpa_calls, tpa_time)
            
            return processed_data
            
        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}
            
            # Log failed audit
            cls._log_audit(customer, user, 'POLICY_DETAILS', execution_id, step_results,
                          total_time, False, error_details, tpa_calls, tpa_time)
            
            logger.error(f"Policy details workflow failed for customer {customer_id}, member {member_code}: {str(e)}")
            raise PolicyWorkflowError(f"Policy details workflow failed: {str(e)}")
    
    @classmethod
    def _get_platform_identity(cls, customer: Customer) -> CustomerPlatformIdentity:
        """Get platform identity for customer"""
        try:
            return customer.platform_identities.filter(
                platform_name='LINE'
            ).first()
        except CustomerPlatformIdentity.DoesNotExist:
            raise CustomerDataError(f"No LINE platform identity found for customer {customer.customer_id}")
    
    @classmethod
    def _get_cached_result(cls, customer: Customer, workflow_type: str, member_code: str = None) -> Optional[Dict]:
        """Get cached workflow result if available and not expired"""
        try:
            cache_entry = CustomerPolicyWorkflowCache.objects.get(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code,
                expires_at__gt=timezone.now()
            )
            return {
                'processed_data': cache_entry.processed_data,
                'execution_id': cache_entry.execution_id
            }
        except CustomerPolicyWorkflowCache.DoesNotExist:
            return None

    @classmethod
    def _cache_result(cls, customer: Customer, workflow_type: str, member_code: Optional[str],
                     platform_identity: CustomerPlatformIdentity, raw_data: Dict, processed_data: Dict,
                     execution_id: str, execution_time: float):
        """Cache workflow result"""
        expires_at = timezone.now() + timedelta(minutes=cls.CACHE_DURATION_MINUTES)

        with transaction.atomic():
            # Delete existing cache entry if exists
            CustomerPolicyWorkflowCache.objects.filter(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code
            ).delete()

            # Create new cache entry
            CustomerPolicyWorkflowCache.objects.create(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code,
                citizen_id=platform_identity.citizen_id,
                social_id=platform_identity.social_id,
                channel_id=platform_identity.channel_id,
                raw_response_data=raw_data,
                processed_data=processed_data,
                execution_id=execution_id,
                execution_time_ms=int(execution_time),
                success=True,
                expires_at=expires_at
            )

    @classmethod
    def _log_audit(cls, customer: Customer, user: User, workflow_type: str, execution_id: str,
                  step_results: Dict, total_time: float, success: bool, error_details: Optional[Dict],
                  tpa_calls: int, tpa_time: float):
        """Log workflow execution audit"""
        PolicyWorkflowAuditLog.objects.create(
            customer=customer,
            requested_by=user,
            workflow_type=workflow_type,
            execution_id=execution_id,
            step_results=step_results,
            total_execution_time_ms=int(total_time),
            success=success,
            error_details=error_details,
            tpa_calls_made=tpa_calls,
            tpa_total_time_ms=int(tpa_time)
        )

    @classmethod
    def _process_policy_list_data(cls, raw_data: Dict) -> Dict[str, Any]:
        """Process and format policy list data for frontend"""
        policy_list = raw_data.get('ListOfPolicyListSocial', [])

        # Extract member codes
        member_codes = []
        for policy in policy_list:
            member_code = policy.get('MemberCode')
            if member_code and member_code not in member_codes:
                member_codes.append(member_code)

        return {
            'policy_list_data': raw_data,
            'member_codes': member_codes,
            'execution_metadata': {
                'total_policies': len(policy_list),
                'unique_member_codes': len(member_codes),
                'processed_at': timezone.now().isoformat()
            }
        }

    @classmethod
    def _process_policy_details_data(cls, raw_data: Dict, member_code: str) -> Dict[str, Any]:
        """Process and format policy details data for frontend"""
        policy_details = raw_data.get('ListOfPolDet', [])
        policy_claims = raw_data.get('ListOfPolClaim', [])

        return {
            'policy_details_data': raw_data,
            'member_code': member_code,
            'execution_metadata': {
                'total_policy_details': len(policy_details),
                'total_claims': len(policy_claims),
                'processed_at': timezone.now().isoformat()
            }
        }
