from django.conf import settings
from typing import Dict, Any

class WorkflowConfigService:
    """Service for managing workflow configuration"""

    DEFAULT_CONFIG = {
        'tpa_api': {
            'base_url': 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2',
            'timeout': 30,
            'max_retries': 3,
            'retry_delay_seconds': 3,
            'credentials': {
                'username': 'BVTPA',
                'password': '*d!n^+Cb@1'
            }
        },
        'cache': {
            'duration_minutes': 30,
            'max_entries_per_customer': 100
        },
        'workflow': {
            'timeout_minutes': 5,
            'enable_audit_logging': True,
            'enable_performance_monitoring': True
        }
    }

    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """Get workflow configuration with environment overrides"""
        config = cls.DEFAULT_CONFIG.copy()

        # Override with Django settings if available
        if hasattr(settings, 'POLICY_WORKFLOW_CONFIG'):
            config.update(settings.POLICY_WORKFLOW_CONFIG)

        return config

    @classmethod
    def get_tpa_credentials(cls) -> Dict[str, str]:
        """Get TPA API credentials securely"""
        config = cls.get_config()
        return config['tpa_api']['credentials']
