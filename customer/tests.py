from django.urls import reverse
from django.test import TestCase
from rest_framework.test import APIRequestFactory, force_authenticate
from rest_framework import status
from unittest.mock import patch, MagicMock
from user.models import User
from customer.models import Customer, Gender, Interface, CustomerPlatformIdentity
from linechatbot.models import LineUserProfile
from llm_rag_doc.models import Document
from .views import *

class CustomerAPITests(TestCase):

    def setUp(self):
        self.factory = APIRequestFactory()
        self.superuser = User.objects.create_superuser(
            username='superuser',
            email='<EMAIL>',
            password='adminpassword',
            confirm_password='adminpassword',
            name='Super User',
            employee_id=1
        )
        self.gender = Gender.objects.create(
            name='Male',
            definition='Male Gender',
            created_by=self.superuser
        )
        self.line_user = LineUserProfile.objects.create(
            line_user_id = "Uxxx01",
            display_name = "Test LINE user 01"
        )
        self.customer_data = {
            'name': 'Test Customer',
            'age': 30,
            'email': '<EMAIL>',
            'phone': '1234567890',
            'gender_id': self.gender.gender_id,
            'line_user_id': self.line_user.line_user_id
        }

    def test_create_customer(self):
        request = self.factory.post(reverse('customer-list'), self.customer_data, format='json')
        force_authenticate(request, user=self.superuser)
        view = CustomerListCreateView.as_view()
        response = view(request)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Customer.objects.count(), 1)
        self.assertEqual(Customer.objects.get(name='Test Customer').email, '<EMAIL>')
        self.assertEqual(Customer.objects.get(name='Test Customer').created_by.name, 'Super User')
        self.assertEqual(response.data['message'], 'Customer Created Successfully')

#     def test_update_customer(self):
#         customer = Customer.objects.create(
#             name='Test Customer',
#             age=30,
#             email='<EMAIL>',
#             phone='1234567890',
#             gender_id=self.gender,
#         )
        
#         update_data = {
#             'name': 'Updated Customer',
#             'age': 35,
#             'email': '<EMAIL>',
#             'phone': '0987654321',
#             'gender_id': self.gender.gender_id,
#         }
#         request = self.factory.put(reverse('customer-detail', kwargs={'pk': customer.customer_id}), update_data, format='json')
#         force_authenticate(request, user=self.superuser)
#         view = CustomerRetrieveUpdateDeleteView.as_view()
#         response = view(request, pk=customer.customer_id)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         customer.refresh_from_db()
#         self.assertEqual(customer.name, 'Updated Customer')
#         self.assertEqual(customer.email, '<EMAIL>')
#         self.assertEqual(customer.updated_by.name, 'Super User')
#         self.assertEqual(response.data['message'], 'Customer Updated Successfully')

#     def test_delete_customer(self):
#         customer = Customer.objects.create(
#             name='Test Customer',
#             age=30,
#             email='<EMAIL>',
#             phone='1234567890',
#             gender_id=self.gender
#         )
#         request = self.factory.delete(reverse('customer-detail', kwargs={'pk': customer.customer_id}))
#         force_authenticate(request, user=self.superuser)
#         view = CustomerRetrieveUpdateDeleteView.as_view()
#         response = view(request, pk=customer.customer_id)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(Customer.objects.filter(name='Test Customer').count(), 0)
#         self.assertEqual(response.data['message'], 'Customer Deleted Successfully')

# class GenderAPITests(TestCase):

#     def setUp(self):
#         self.factory = APIRequestFactory()
#         self.superuser = User.objects.create_superuser(
#             username='superuser',
#             email='<EMAIL>',
#             password='adminpassword',
#             confirm_password='adminpassword',
#             name='Super User',
#             employee_id=1
#         )
#         self.gender_data = {
#             'name': 'Female',
#             'definition': 'Female Gender',
#         }

#     def test_create_gender(self):
#         request = self.factory.post(reverse('gender-list'), self.gender_data, format='json')
#         force_authenticate(request, user=self.superuser)
#         view = GenderListCreateView.as_view()
#         response = view(request)
#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         self.assertEqual(Gender.objects.count(), 1)
#         self.assertEqual(Gender.objects.get(name='Female').definition, 'Female Gender')
#         self.assertEqual(Gender.objects.get(name='Female').created_by.name, 'Super User')
#         self.assertEqual(response.data['message'], 'Gender Created Successfully')

#     def test_update_gender(self):
#         gender = Gender.objects.create(
#             name='Female',
#             definition='Female Gender',
#             created_by=self.superuser
#         )
        
#         update_data = {
#             'name': 'Updated Gender',
#             'definition': 'Updated Gender Definition',
#         }
#         request = self.factory.put(reverse('gender-detail', kwargs={'pk': gender.gender_id}), update_data, format='json')
#         force_authenticate(request, user=self.superuser)
#         view = GenderRetrieveUpdateDeleteView.as_view()
#         response = view(request, pk=gender.gender_id)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         gender.refresh_from_db()
#         self.assertEqual(gender.name, 'Updated Gender')
#         self.assertEqual(gender.definition, 'Updated Gender Definition')
#         self.assertEqual(gender.updated_by.name, 'Super User')
#         self.assertEqual(response.data['message'], 'Gender Updated Successfully')

#     def test_delete_gender(self):
#         gender = Gender.objects.create(
#             name='Female',
#             definition='Female Gender',
#             created_by=self.superuser
#         )
#         request = self.factory.delete(reverse('gender-detail', kwargs={'pk': gender.gender_id}))
#         force_authenticate(request, user=self.superuser)
#         view = GenderRetrieveUpdateDeleteView.as_view()
#         response = view(request, pk=gender.gender_id)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         gender.refresh_from_db()
#         self.assertFalse(gender.is_active)
#         self.assertEqual(response.data['message'], 'Gender Deleted Successfully')

# class InterfaceAPITests(TestCase):

#     def setUp(self):
#         self.factory = APIRequestFactory()
#         self.superuser = User.objects.create_superuser(
#             username='superuser',
#             email='<EMAIL>',
#             password='adminpassword',
#             confirm_password='adminpassword',
#             name='Super User',
#             employee_id=1
#         )
#         self.interface_valid_data = {
#             'name': 'LINE',
#             'definition': 'Line'
#         }
#         self.interface_invalid_data = {
#             'name': 'asd LINE xxxx',
#             'definition': 'Line xxx'
#         }

#         self.interface = Interface.objects.create(
#             name = 'NONE',
#             definition = 'None'
#         )

#     def test_create_valid_interface(self):
#         request = self.factory.post(reverse('interface-list'), self.interface_valid_data, format='json')
#         force_authenticate(request, user=self.superuser)
#         view = InterfaceListCreateView.as_view()
#         response = view(request)

#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         self.assertEqual(Interface.objects.count(), 2)
#         self.assertEqual(Interface.objects.get(name='LINE').name, 'LINE')
#         self.assertEqual(Interface.objects.get(name='LINE').is_active, True)

#     def test_create_invalid_interface(self):
#         request = self.factory.post(reverse('interface-list'), self.interface_invalid_data, format='json')
#         force_authenticate(request, user=self.superuser)
#         view = InterfaceListCreateView.as_view()
#         response = view(request)

#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.data['name'][0].code, 'invalid_choice')
    
#     def test_create_duplicated_interface(self):
#         interface_duplicated_data = {
#             'name': 'NONE',
#             'definition': 'None'
#         }
#         request = self.factory.post(reverse('interface-list'), interface_duplicated_data, format='json')
#         force_authenticate(request, user=self.superuser)
#         view = InterfaceListCreateView.as_view()
#         response = view(request)

#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.data['name'][0].code, 'unique')



#     def test_update_interface(self):
#         interface_update_data = {
#             'name': 'FBMESSENGER',
#             'definition': 'FBMessenger'
#         }
#         request = self.factory.put(reverse('interface-detail', kwargs={'pk': self.interface.pk}), interface_update_data, format='json')
#         force_authenticate(request, user=self.superuser)
#         view = InterfaceRetrieveUpdateDeleteView.as_view()
#         response = view(request, pk=self.interface.id)

#         self.interface.refresh_from_db()
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(Interface.objects.get(name='FBMESSENGER').definition, 'FBMessenger')
#         self.assertEqual(response.data['message'], 'Interface Updated Successfully')


#     def test_delete_interface(self):
#         request = self.factory.delete(reverse('interface-detail', kwargs={'pk': self.interface.pk}))
#         force_authenticate(request, user=self.superuser)
#         view = InterfaceRetrieveUpdateDeleteView.as_view()
#         response = view(request, pk=self.interface.id)

#         self.interface.refresh_from_db()
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertFalse(self.interface.is_active)
#         self.assertEqual(response.data['message'], 'Interface Deleted Successfully')














# from django.test import TestCase
# from django.urls import reverse
# from django.core.files.uploadedfile import SimpleUploadedFile
# # from django.contrib.auth.models import User
# from rest_framework.test import APIClient
# from rest_framework import status
# from unittest.mock import patch, MagicMock
# from user.models import User
# from .models import Customer, Gender, Interface, LineUserProfile

# class CustomerFileAPITests(TestCase):
#     def setUp(self):
#         """Set up test data"""
#         # Create test user
#         self.user = User.objects.create_user(
#             username='testuser',
#             password='testpass123',
#             confirm_password='testpass123',
#             email='<EMAIL>',

#         )
        
#         # Create required related objects
#         self.gender = Gender.objects.create(name='Male')
#         self.interface = Interface.objects.create(name='Web')
#         self.line_profile = LineUserProfile.objects.create(
#             line_user_id='test123',
#             display_name='Test User'
#         )
        
#         # Create test customer
#         self.customer = Customer.objects.create(
#             name='Test Customer',
#             email='<EMAIL>',
#             phone='1234567890',
#             gender_id=self.gender,
#             main_interface_id=self.interface,
#             line_user_id=self.line_profile,
#             created_by=self.user
#         )
        
#         # Setup API client
#         self.client = APIClient()
        
#         # Create a test file
#         self.test_file = SimpleUploadedFile(
#             "test_file.txt",
#             b"Test file content",
#             content_type="text/plain"
#         )

#     @patch('utils.azure_storage.AzureBlobStorage')
#     def test_file_upload_success(self, mock_storage):
#         """Test successful file upload"""
#         # Mock the Azure storage response
#         mock_instance = mock_storage.return_value
#         mock_instance.upload_file.return_value = 'https://test-url.com/test_file.txt'
        
#         url = reverse('customer-file-upload', args=[self.customer.customer_id])
#         response = self.client.post(url, {
#             'file': self.test_file
#         }, format='multipart')
        
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data['url'], 'https://test-url.com/test_file.txt')
#         mock_instance.upload_file.assert_called_once()

#     @patch('utils.azure_storage.AzureBlobStorage')
#     def test_file_upload_customer_not_found(self, mock_storage):
#         """Test file upload with non-existent customer"""
#         url = reverse('customer-file-upload', args=[99999])
#         response = self.client.post(url, {
#             'file': self.test_file
#         }, format='multipart')
        
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         mock_storage.assert_not_called()

#     @patch('utils.azure_storage.AzureBlobStorage')
#     def test_file_upload_no_file(self, mock_storage):
#         """Test file upload without file"""
#         url = reverse('customer-file-upload', args=[self.customer.customer_id])
#         response = self.client.post(url, {}, format='multipart')
        
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         mock_storage.assert_not_called()

#     @patch('utils.azure_storage.AzureBlobStorage')
#     def test_file_list_success(self, mock_storage):
#         """Test successful file listing"""
#         mock_instance = mock_storage.return_value
#         mock_instance.list_files.return_value = [
#             {
#                 'name': 'test1.txt',
#                 'size': 100,
#                 'created_on': '2024-01-20T12:00:00Z',
#                 'url': 'https://test-url.com/test1.txt'
#             }
#         ]
        
#         url = reverse('customer-file-list', args=[self.customer.customer_id])
#         response = self.client.get(url)
        
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(len(response.data), 1)
#         self.assertEqual(response.data[0]['name'], 'test1.txt')
#         mock_instance.list_files.assert_called_once()

#     @patch('utils.azure_storage.AzureBlobStorage')
#     def test_file_delete_success(self, mock_storage):
#         """Test successful file deletion"""
#         mock_instance = mock_storage.return_value
#         mock_instance.delete_file.return_value = True
        
#         url = reverse('customer-file-delete', args=[
#             self.customer.customer_id,
#             'test_file.txt'
#         ])
#         response = self.client.delete(url)
        
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         mock_instance.delete_file.assert_called_once()

#     @patch('utils.azure_storage.AzureBlobStorage')
#     def test_file_delete_error(self, mock_storage):
#         """Test file deletion with Azure error"""
#         mock_instance = mock_storage.return_value
#         mock_instance.delete_file.side_effect = Exception('Azure error')
        
#         url = reverse('customer-file-delete', args=[
#             self.customer.customer_id,
#             'test_file.txt'
#         ])
#         response = self.client.delete(url)
        
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertIn('error', response.data)

#     def test_invalid_customer_requests(self):
#         """Test all endpoints with invalid customer ID"""
#         invalid_id = 99999
        
#         # Test upload
#         url = reverse('customer-file-upload', args=[invalid_id])
#         response = self.client.post(url, {'file': self.test_file}, format='multipart')
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
#         # Test list
#         url = reverse('customer-file-list', args=[invalid_id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
#         # Test delete
#         url = reverse('customer-file-delete', args=[invalid_id, 'test.txt'])
#         response = self.client.delete(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

#     def test_missing_file_upload(self):
#         """Test file upload without file in request"""
#         url = reverse('customer-file-upload', args=[self.customer.customer_id])
#         response = self.client.post(url, {}, format='multipart')
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

#     @patch('utils.azure_storage.AzureBlobStorage')
#     def test_azure_connection_error(self, mock_storage):
#         """Test handling of Azure connection errors"""
#         mock_instance = mock_storage.return_value
#         mock_instance.upload_file.side_effect = Exception('Connection error')
        
#         url = reverse('customer-file-upload', args=[self.customer.customer_id])
#         response = self.client.post(url, {
#             'file': self.test_file
#         }, format='multipart')
        
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class PolicyWorkflowAPITests(TestCase):
    """Test cases for policy workflow API endpoints"""

    def setUp(self):
        self.factory = APIRequestFactory()
        self.superuser = User.objects.create_superuser(
            username='testuser',
            email='<EMAIL>',
            password='testpassword',
            confirm_password='testpassword',
            name='Test User',
            employee_id=2
        )

        # Create a test customer
        self.customer = Customer.objects.create(
            name='Test Customer',
            email='<EMAIL>',
            created_by=self.superuser
        )

        # Create a platform identity for the customer
        self.platform_identity = CustomerPlatformIdentity.objects.create(
            customer=self.customer,
            platform_name='LINE',
            platform_user_id='U3ef2199803607a9ec643f2461fd2f039',
            channel_id='2006769099',
            display_name='Test User'
        )

    @patch('customer.services.policy_workflow_service.TPAApiService')
    def test_policy_list_workflow_endpoint(self, mock_tpa_service):
        """Test the policy list workflow endpoint"""
        # Mock TPA service responses
        mock_tpa_service.get_bearer_token.return_value = "mock_token"
        mock_tpa_service.verify_citizen_id.return_value = {
            "ListOfSearchCitizenID": [{"CitizenID": "2019086318637", "Status": "1"}]
        }
        mock_tpa_service.check_registration.return_value = {
            "ListOfCheckRegister": [{"Status": "YES"}]
        }
        mock_tpa_service.get_policy_list.return_value = {
            "ListOfPolicyListSocial": [
                {
                    "Name": "Test Policy",
                    "CitizenID": "2019086318637",
                    "PolNo": "TEST_2024",
                    "MemberCode": "TEST-001",
                    "EffFrom": "01/01/2024",
                    "EffTo": "31/12/2024"
                }
            ]
        }

        # Make request to the endpoint
        url = reverse('customer-policy-list-workflow', args=[self.customer.customer_id])
        request = self.factory.get(url)
        force_authenticate(request, user=self.superuser)

        view = CustomerPolicyListWorkflowView.as_view()
        response = view(request, customer_id=self.customer.customer_id)

        # Assertions
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('policy_list_data', response.data)
        self.assertIn('member_codes', response.data)
        self.assertIn('execution_metadata', response.data)

    def test_policy_workflow_customer_not_found(self):
        """Test policy workflow with non-existent customer"""
        url = reverse('customer-policy-list-workflow', args=[99999])
        request = self.factory.get(url)
        force_authenticate(request, user=self.superuser)

        view = CustomerPolicyListWorkflowView.as_view()
        response = view(request, customer_id=99999)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'Customer not found')
#         self.assertIn('error', response.data)