# Policy Workflow System Migration Plan
## Frontend to Backend Migration

### Overview
This document outlines the comprehensive migration plan for moving the policy workflow system from frontend execution to backend implementation. The migration involves creating new REST API endpoints that handle TPA (Third Party Administrator) integration, workflow execution, and policy data processing.

### Current System Analysis

#### Frontend Components to Migrate
- **Configuration Files:**
  - `src/lib/api/features/customer/policy-list-workflow.json` - Policy list workflow configuration
  - `src/lib/api/features/customer/policy-details-workflow.json` - Policy details workflow configuration

- **Service Files:**
  - `src/lib/api/features/customer/policy-workflow-executor.service.ts` - Generic workflow executor
  - `src/lib/api/features/customer/policy-workflow-executor.types.ts` - Type definitions
  - `src/lib/api/features/customer/policy-workflow-executor.utils.ts` - Utility functions

- **Integration Points:**
  - `src/lib/api/features/customer/customers.service.ts` methods:
    - `getCustomerPolicyList()` - Currently executes policy list workflow
    - `getCustomerPolicyDetails()` - Currently executes policy details workflow

#### Current Workflow Analysis
Both workflows follow a similar pattern:
1. **Authentication:** Get bearer token from TPA API
2. **Verification:** Verify citizen ID and registration status
3. **Data Retrieval:** Fetch policy list or policy details
4. **Processing:** Extract and format data for frontend consumption

### Backend Implementation Plan

#### 1. Database Schema Changes

##### New Models
Create new Django models to support policy workflow caching and audit:

```python
# customer/models.py additions

class CustomerPolicyWorkflowCache(models.Model):
    """Cache for policy workflow results to improve performance"""
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='policy_cache')
    workflow_type = models.CharField(max_length=20, choices=[
        ('POLICY_LIST', 'Policy List'),
        ('POLICY_DETAILS', 'Policy Details')
    ])
    member_code = models.CharField(max_length=100, null=True, blank=True)  # For policy details
    citizen_id = models.CharField(max_length=20)
    social_id = models.CharField(max_length=100)
    channel_id = models.CharField(max_length=100)
    
    # Cached data
    raw_response_data = models.JSONField()
    processed_data = models.JSONField()
    
    # Metadata
    execution_id = models.CharField(max_length=50, unique=True)
    execution_time_ms = models.IntegerField()
    success = models.BooleanField(default=True)
    error_message = models.TextField(null=True, blank=True)
    
    # Timestamps
    created_on = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()  # Cache expiration
    
    class Meta:
        indexes = [
            models.Index(fields=['customer', 'workflow_type']),
            models.Index(fields=['customer', 'member_code']),
            models.Index(fields=['execution_id']),
            models.Index(fields=['expires_at']),
        ]
        unique_together = [['customer', 'workflow_type', 'member_code']]

class PolicyWorkflowAuditLog(models.Model):
    """Audit log for policy workflow executions"""
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    workflow_type = models.CharField(max_length=20)
    execution_id = models.CharField(max_length=50)
    
    # Request details
    requested_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True)
    request_ip = models.GenericIPAddressField(null=True)
    user_agent = models.TextField(null=True)
    
    # Execution details
    step_results = models.JSONField()  # Detailed step execution results
    total_execution_time_ms = models.IntegerField()
    success = models.BooleanField()
    error_details = models.JSONField(null=True)
    
    # TPA API details
    tpa_calls_made = models.IntegerField(default=0)
    tpa_total_time_ms = models.IntegerField(default=0)
    
    created_on = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['customer', 'created_on']),
            models.Index(fields=['execution_id']),
            models.Index(fields=['workflow_type', 'created_on']),
        ]
```

#### 2. Backend Services Implementation

##### TPA Integration Service
Create a dedicated service for TPA API interactions:

```python
# customer/services/tpa_service.py

import requests
import logging
from typing import Dict, Any, Optional
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

class TPAApiService:
    """Service for interacting with Third Party Administrator APIs"""
    
    BASE_URL = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2"
    TIMEOUT = 30
    MAX_RETRIES = 3
    
    @classmethod
    def get_bearer_token(cls, social_id: str, channel_id: str, channel: str = "LINE") -> str:
        """Get bearer token from TPA API"""
        endpoint = f"{cls.BASE_URL}/api/GetToken"
        payload = {
            "USERNAME": "BVTPA",
            "PASSWORD": "*d!n^+Cb@1",
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel
        }
        
        response = cls._make_request("POST", endpoint, json=payload)
        return response  # Token is returned directly as string
    
    @classmethod
    def verify_citizen_id(cls, bearer_token: str, social_id: str, channel_id: str, 
                         channel: str = "LINE") -> Dict[str, Any]:
        """Verify citizen ID with TPA API"""
        endpoint = f"{cls.BASE_URL}/api/SearchCitizenID"
        headers = {"Authorization": f"Bearer {bearer_token}"}
        payload = {
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel
        }
        
        response = cls._make_request("POST", endpoint, json=payload, headers=headers)
        
        # Validate response
        if not response.get("ListOfSearchCitizenID") or \
           response["ListOfSearchCitizenID"][0].get("Status") != "1":
            raise ValueError("Citizen ID verification failed")
        
        return response
    
    @classmethod
    def check_registration(cls, bearer_token: str, citizen_id: str, social_id: str,
                          channel_id: str, channel: str = "LINE") -> Dict[str, Any]:
        """Check registration status with TPA API"""
        endpoint = f"{cls.BASE_URL}/api/CheckRegister"
        headers = {"Authorization": f"Bearer {bearer_token}"}
        payload = {
            "CITIZEN_ID": citizen_id,
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel,
            "CONTACT": ""
        }
        
        response = cls._make_request("POST", endpoint, json=payload, headers=headers)
        
        # Validate response
        if not response.get("ListOfCheckRegister") or \
           response["ListOfCheckRegister"][0].get("Status") != "YES":
            raise ValueError("Registration check failed")
        
        return response
    
    @classmethod
    def get_policy_list(cls, bearer_token: str, citizen_id: str, social_id: str,
                       channel_id: str, channel: str = "LINE") -> Dict[str, Any]:
        """Get policy list from TPA API"""
        endpoint = f"{cls.BASE_URL}/api/PolicyListSocial"
        headers = {"Authorization": f"Bearer {bearer_token}"}
        payload = {
            "CITIZEN_ID": citizen_id,
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel
        }
        
        return cls._make_request("POST", endpoint, json=payload, headers=headers)
    
    @classmethod
    def get_policy_details(cls, bearer_token: str, social_id: str, channel_id: str,
                          member_code: str, channel: str = "LINE") -> Dict[str, Any]:
        """Get policy details from TPA API"""
        endpoint = f"{cls.BASE_URL}/api/PolicyDetailSocial"
        headers = {"Authorization": f"Bearer {bearer_token}"}
        payload = {
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel,
            "MEMBER_CODE": member_code
        }
        
        return cls._make_request("POST", endpoint, json=payload, headers=headers)
    
    @classmethod
    def _make_request(cls, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with retry logic"""
        kwargs.setdefault('timeout', cls.TIMEOUT)
        
        for attempt in range(cls.MAX_RETRIES):
            try:
                response = requests.request(method, url, **kwargs)
                response.raise_for_status()
                
                # Handle both JSON and plain text responses
                content_type = response.headers.get('content-type', '')
                if 'application/json' in content_type:
                    return response.json()
                else:
                    return response.text
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"TPA API request attempt {attempt + 1} failed: {str(e)}")
                if attempt == cls.MAX_RETRIES - 1:
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff
```

##### Policy Workflow Service
Create the main workflow execution service:

```python
# customer/services/policy_workflow_service.py

import time
import uuid
from typing import Dict, Any, Optional, Tuple
from django.utils import timezone
from django.core.cache import cache
from customer.models import Customer, CustomerPlatformIdentity, CustomerPolicyWorkflowCache
from customer.services.tpa_service import TPAApiService

class PolicyWorkflowService:
    """Service for executing policy workflows"""
    
    CACHE_DURATION_MINUTES = 30
    
    @classmethod
    def execute_policy_list_workflow(cls, customer_id: int, 
                                   user=None) -> Dict[str, Any]:
        """Execute policy list workflow for a customer"""
        start_time = time.time()
        execution_id = f"policy_list_{int(start_time)}_{uuid.uuid4().hex[:8]}"
        
        try:
            # Get customer and platform identity
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_customer_platform_identity(customer)
            
            # Check cache first
            cached_result = cls._get_cached_result(customer, 'POLICY_LIST')
            if cached_result:
                return cached_result
            
            # Execute workflow steps
            bearer_token = TPAApiService.get_bearer_token(
                platform_identity['social_id'],
                platform_identity['channel_id']
            )
            
            citizen_verification = TPAApiService.verify_citizen_id(
                bearer_token,
                platform_identity['social_id'],
                platform_identity['channel_id']
            )
            
            citizen_id = citizen_verification['ListOfSearchCitizenID'][0]['CitizenID']
            
            registration_check = TPAApiService.check_registration(
                bearer_token,
                citizen_id,
                platform_identity['social_id'],
                platform_identity['channel_id']
            )
            
            policy_list_response = TPAApiService.get_policy_list(
                bearer_token,
                citizen_id,
                platform_identity['social_id'],
                platform_identity['channel_id']
            )
            
            # Process and format response
            processed_data = cls._process_policy_list_response(
                policy_list_response, customer_id, execution_id
            )
            
            # Cache the result
            cls._cache_workflow_result(
                customer, 'POLICY_LIST', None, citizen_id,
                platform_identity, policy_list_response, processed_data,
                execution_id, int((time.time() - start_time) * 1000)
            )
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Policy list workflow failed for customer {customer_id}: {str(e)}")
            raise
    
    @classmethod
    def execute_policy_details_workflow(cls, customer_id: int, member_code: str,
                                      user=None) -> Dict[str, Any]:
        """Execute policy details workflow for a specific member code"""
        start_time = time.time()
        execution_id = f"policy_details_{int(start_time)}_{uuid.uuid4().hex[:8]}"
        
        try:
            # Get customer and platform identity
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_customer_platform_identity(customer)
            
            # Check cache first
            cached_result = cls._get_cached_result(customer, 'POLICY_DETAILS', member_code)
            if cached_result:
                return cached_result
            
            # Execute workflow steps
            bearer_token = TPAApiService.get_bearer_token(
                platform_identity['social_id'],
                platform_identity['channel_id']
            )
            
            citizen_verification = TPAApiService.verify_citizen_id(
                bearer_token,
                platform_identity['social_id'],
                platform_identity['channel_id']
            )
            
            policy_details_response = TPAApiService.get_policy_details(
                bearer_token,
                platform_identity['social_id'],
                platform_identity['channel_id'],
                member_code
            )
            
            # Process and format response
            processed_data = cls._process_policy_details_response(
                policy_details_response, customer_id, member_code, execution_id
            )
            
            # Cache the result
            citizen_id = citizen_verification['ListOfSearchCitizenID'][0]['CitizenID']
            cls._cache_workflow_result(
                customer, 'POLICY_DETAILS', member_code, citizen_id,
                platform_identity, policy_details_response, processed_data,
                execution_id, int((time.time() - start_time) * 1000)
            )
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Policy details workflow failed for customer {customer_id}, member {member_code}: {str(e)}")
            raise
```

#### 3. API Endpoints Implementation

##### New Views
Create new API views in `customer/views.py`:

```python
class CustomerPolicyListWorkflowView(APIView):
    """Execute policy list workflow and return policy list"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        """
        Execute policy list workflow for customer
        
        Returns:
            - policy_list_data: Raw policy list from TPA
            - member_codes: Extracted member codes
            - execution_metadata: Workflow execution details
        """
        try:
            result = PolicyWorkflowService.execute_policy_list_workflow(
                customer_id=customer_id,
                user=request.user
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Customer.DoesNotExist:
            return Response({
                'error': 'Customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Policy list workflow error: {str(e)}")
            return Response({
                'error': f'Workflow execution failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CustomerPolicyDetailsWorkflowView(APIView):
    """Execute policy details workflow for specific member code"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id, member_code):
        """
        Execute policy details workflow for customer and member code
        
        Returns:
            - policy_details_data: Policy details and claims data
            - member_code: The requested member code
            - execution_metadata: Workflow execution details
        """
        try:
            result = PolicyWorkflowService.execute_policy_details_workflow(
                customer_id=customer_id,
                member_code=member_code,
                user=request.user
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Customer.DoesNotExist:
            return Response({
                'error': 'Customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Policy details workflow error: {str(e)}")
            return Response({
                'error': f'Workflow execution failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

##### URL Configuration
Update `customer/urls.py`:

```python
# Add to urlpatterns
path('api/customers/<int:customer_id>/policies/workflow/', 
     views.CustomerPolicyListWorkflowView.as_view(), 
     name='customer-policy-list-workflow'),
path('api/customers/<int:customer_id>/policies/<str:member_code>/details/workflow/', 
     views.CustomerPolicyDetailsWorkflowView.as_view(), 
     name='customer-policy-details-workflow'),
```

### Frontend Changes

#### Updated Service Methods
Modify `src/lib/api/features/customer/customers.service.ts`:

```typescript
/**
 * Get customer policy list using backend workflow execution
 */
async getCustomerPolicyList(id: string, token: string): Promise<any> {
    try {
        console.log(`Calling backend policy list workflow for customer ${id}`);

        const response = await fetch(`${this.baseUrl}/api/customers/${id}/policies/workflow/`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new ApiError(JSON.stringify(errorData), response.status);
        }

        const responseData = await response.json();
        
        console.log(`Backend policy list workflow successful for customer ${id}`, {
            policies_count: responseData.policy_list_data?.ListOfPolicyListSocial?.length || 0,
            member_codes_count: responseData.member_codes?.length || 0,
            execution_time: responseData.execution_metadata?.total_execution_time_ms
        });

        return {
            policy_list_data: responseData,
            res_status: 200
        };

    } catch (error) {
        console.error(`Backend policy list workflow failed for customer ${id}:`, error);
        throw error;
    }
}

/**
 * Get detailed policy information using backend workflow execution
 */
async getCustomerPolicyDetails(id: string, memberCode: string, token: string): Promise<any> {
    try {
        console.log(`Calling backend policy details workflow for customer ${id}, member code ${memberCode}`);

        const response = await fetch(`${this.baseUrl}/api/customers/${id}/policies/${memberCode}/details/workflow/`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new ApiError(JSON.stringify(errorData), response.status);
        }

        const responseData = await response.json();
        
        console.log(`Backend policy details workflow successful for customer ${id}`, {
            policy_details_count: responseData.policy_details_data?.ListOfPolDet?.length || 0,
            claims_count: responseData.policy_details_data?.ListOfPolClaim?.length || 0,
            execution_time: responseData.execution_metadata?.total_execution_time_ms
        });

        return {
            policy_details_data: responseData,
            res_status: 200
        };

    } catch (error) {
        console.error(`Backend policy details workflow failed for customer ${id}, member ${memberCode}:`, error);
        throw error;
    }
}
```

### Migration Strategy

#### Phase 1: Backend Implementation (Week 1-2)
1. **Database Setup**
   - Create migration files for new models
   - Run migrations in development environment
   - Set up database indexes for performance

2. **Service Implementation**
   - Implement `TPAApiService` with all TPA API integrations
   - Implement `PolicyWorkflowService` with workflow logic
   - Add comprehensive error handling and logging
   - Implement caching mechanism

3. **API Endpoints**
   - Create new API views and URL patterns
   - Add authentication and permission checks
   - Implement request/response serialization
   - Add comprehensive API documentation

#### Phase 2: Testing (Week 2-3)
1. **Unit Tests**
   - Test TPA service methods with mocked responses
   - Test workflow service logic
   - Test API endpoints with various scenarios

2. **Integration Tests**
   - Test end-to-end workflow execution
   - Test error handling and retry logic
   - Test caching behavior
   - Performance testing with load simulation

#### Phase 3: Frontend Migration (Week 3)
1. **Service Updates**
   - Update `customers.service.ts` methods
   - Remove workflow executor dependencies
   - Update error handling for new API responses

2. **Testing**
   - Test frontend integration with new backend APIs
   - Verify data format compatibility
   - Test error scenarios and user experience

#### Phase 4: Deployment (Week 4)
1. **Staging Deployment**
   - Deploy backend changes to staging
   - Run comprehensive testing
   - Performance monitoring and optimization

2. **Production Deployment**
   - Deploy with feature flags for gradual rollout
   - Monitor system performance and error rates
   - Gradual migration of users to new system

3. **Cleanup**
   - Remove old frontend workflow files after successful migration
   - Update documentation
   - Archive old code for reference

### Testing Strategy

#### Backend Testing
1. **Unit Tests**
   - TPA API service methods
   - Workflow execution logic
   - Data processing and formatting
   - Cache management

2. **Integration Tests**
   - End-to-end workflow execution
   - Database operations
   - API endpoint responses
   - Error handling scenarios

3. **Performance Tests**
   - Load testing with concurrent requests
   - Cache performance validation
   - TPA API timeout handling
   - Memory usage monitoring

#### Frontend Testing
1. **Service Integration Tests**
   - API call formatting and responses
   - Error handling and user feedback
   - Data compatibility with existing components

2. **User Acceptance Tests**
   - Policy list loading functionality
   - Policy details retrieval
   - Error scenarios and recovery
   - Performance from user perspective

### Deployment Considerations

#### Environment Configuration
1. **TPA API Settings**
   - Configure TPA base URLs for different environments
   - Secure credential management
   - Timeout and retry configurations

2. **Caching Configuration**
   - Redis/database cache settings
   - Cache expiration policies
   - Cache invalidation strategies

3. **Monitoring and Logging**
   - Comprehensive logging for workflow execution
   - Performance metrics collection
   - Error tracking and alerting
   - Audit trail for compliance

#### Security Considerations
1. **API Security**
   - JWT authentication validation
   - Rate limiting for API endpoints
   - Input validation and sanitization
   - SQL injection prevention

2. **Data Protection**
   - Encryption of sensitive data in cache
   - Secure TPA credential storage
   - Audit logging for data access
   - GDPR compliance for data retention

#### Performance Optimization
1. **Caching Strategy**
   - Intelligent cache invalidation
   - Cache warming for frequently accessed data
   - Distributed caching for scalability

2. **Database Optimization**
   - Proper indexing for query performance
   - Connection pooling configuration
   - Query optimization and monitoring

3. **API Performance**
   - Response compression
   - Pagination for large datasets
   - Asynchronous processing where applicable

### Success Metrics

#### Performance Metrics
- API response time < 2 seconds for policy list
- API response time < 3 seconds for policy details
- Cache hit rate > 80% for repeated requests
- TPA API success rate > 99%

#### Reliability Metrics
- System uptime > 99.9%
- Error rate < 0.1%
- Successful workflow completion rate > 99%

#### User Experience Metrics
- Reduced frontend bundle size (removal of workflow executor)
- Improved page load times
- Better error handling and user feedback

### Risk Mitigation

#### Technical Risks
1. **TPA API Dependency**
   - Implement circuit breaker pattern
   - Graceful degradation when TPA is unavailable
   - Comprehensive retry logic with exponential backoff

2. **Data Consistency**
   - Implement proper transaction handling
   - Cache invalidation strategies
   - Data validation at multiple layers

3. **Performance Issues**
   - Load testing before deployment
   - Performance monitoring and alerting
   - Scalability planning for increased load

#### Business Risks
1. **Migration Downtime**
   - Blue-green deployment strategy
   - Feature flags for gradual rollout
   - Rollback procedures

2. **Data Loss**
   - Comprehensive backup strategies
   - Data migration validation
   - Audit trails for all operations

## API Specifications

### Endpoint 1: Policy List Workflow
**URL:** `GET /api/customers/{customer_id}/policies/workflow/`

**Request Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Response Format:**
```json
{
  "policy_list_data": {
    "ListOfPolicyListSocial": [
      {
        "Name": "ทดสอบ2",
        "CitizenID": "2019086318637",
        "PolNo": "BVTPA_2024",
        "MemberCode": "*********** Chatbot4",
        "EffFrom": "15/02/2024",
        "EffTo": "20/12/2025"
      }
    ]
  },
  "member_codes": ["*********** Chatbot4"],
  "execution_metadata": {
    "execution_id": "policy_list_1703123456_abc12345",
    "customer_id": "123",
    "started_at": "2024-01-15T10:30:00Z",
    "completed_at": "2024-01-15T10:30:03Z",
    "total_execution_time_ms": 3250,
    "cached": false,
    "step_results": [
      {
        "step_id": 1,
        "step_name": "get_bearer_token",
        "success": true,
        "execution_time_ms": 1200
      },
      {
        "step_id": 2,
        "step_name": "verify_citizen_id",
        "success": true,
        "execution_time_ms": 800
      },
      {
        "step_id": 3,
        "step_name": "verify_registration",
        "success": true,
        "execution_time_ms": 650
      },
      {
        "step_id": 4,
        "step_name": "fetch_policy_list",
        "success": true,
        "execution_time_ms": 600
      }
    ]
  }
}
```

**Error Response:**
```json
{
  "error": "Workflow execution failed: Citizen ID verification failed",
  "error_code": "WORKFLOW_EXECUTION_ERROR",
  "execution_id": "policy_list_1703123456_abc12345",
  "step_failed": 2,
  "details": {
    "tpa_response": "Invalid citizen ID status"
  }
}
```

### Endpoint 2: Policy Details Workflow
**URL:** `GET /api/customers/{customer_id}/policies/{member_code}/details/workflow/`

**Request Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Response Format:**
```json
{
  "policy_details_data": {
    "ListOfPolDet": [
      {
        "PolicyNumber": "BVTPA_2024",
        "MemberCode": "*********** Chatbot4",
        "Coverage": "Health Insurance",
        "Premium": "5000.00",
        "Status": "Active"
      }
    ],
    "ListOfPolClaim": [
      {
        "ClaimNumber": "CLM001",
        "ClaimDate": "2024-01-10",
        "Amount": "1500.00",
        "Status": "Approved"
      }
    ]
  },
  "member_code": "*********** Chatbot4",
  "execution_metadata": {
    "execution_id": "policy_details_1703123456_def67890",
    "customer_id": "123",
    "member_code": "*********** Chatbot4",
    "started_at": "2024-01-15T10:35:00Z",
    "completed_at": "2024-01-15T10:35:02Z",
    "total_execution_time_ms": 2100,
    "cached": true,
    "cache_hit_at": "2024-01-15T10:32:00Z"
  }
}
```

## Additional Implementation Details

### Configuration Management
Create a configuration service for workflow settings:

```python
# customer/services/workflow_config_service.py

from django.conf import settings
from typing import Dict, Any

class WorkflowConfigService:
    """Service for managing workflow configuration"""

    DEFAULT_CONFIG = {
        'tpa_api': {
            'base_url': 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2',
            'timeout': 30,
            'max_retries': 3,
            'retry_delay_seconds': 3,
            'credentials': {
                'username': 'BVTPA',
                'password': '*d!n^+Cb@1'
            }
        },
        'cache': {
            'duration_minutes': 30,
            'max_entries_per_customer': 100
        },
        'workflow': {
            'timeout_minutes': 5,
            'enable_audit_logging': True,
            'enable_performance_monitoring': True
        }
    }

    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """Get workflow configuration with environment overrides"""
        config = cls.DEFAULT_CONFIG.copy()

        # Override with Django settings if available
        if hasattr(settings, 'POLICY_WORKFLOW_CONFIG'):
            config.update(settings.POLICY_WORKFLOW_CONFIG)

        return config

    @classmethod
    def get_tpa_credentials(cls) -> Dict[str, str]:
        """Get TPA API credentials securely"""
        config = cls.get_config()
        return config['tpa_api']['credentials']
```

### Error Handling and Logging
Implement comprehensive error handling:

```python
# customer/exceptions.py

class PolicyWorkflowError(Exception):
    """Base exception for policy workflow errors"""
    def __init__(self, message: str, error_code: str = None, step_id: int = None):
        self.message = message
        self.error_code = error_code
        self.step_id = step_id
        super().__init__(self.message)

class TPAApiError(PolicyWorkflowError):
    """Exception for TPA API related errors"""
    pass

class WorkflowValidationError(PolicyWorkflowError):
    """Exception for workflow validation errors"""
    pass

class CustomerDataError(PolicyWorkflowError):
    """Exception for customer data related errors"""
    pass
```

### Monitoring and Metrics
Implement monitoring for the workflow system:

```python
# customer/services/workflow_monitoring_service.py

import time
import logging
from django.core.cache import cache
from django.db.models import Count, Avg
from customer.models import PolicyWorkflowAuditLog

logger = logging.getLogger(__name__)

class WorkflowMonitoringService:
    """Service for monitoring workflow performance and health"""

    @classmethod
    def record_workflow_execution(cls, execution_data: Dict[str, Any]):
        """Record workflow execution metrics"""
        try:
            # Store in audit log
            PolicyWorkflowAuditLog.objects.create(**execution_data)

            # Update real-time metrics in cache
            cache_key = f"workflow_metrics_{execution_data['workflow_type']}"
            metrics = cache.get(cache_key, {
                'total_executions': 0,
                'successful_executions': 0,
                'average_execution_time': 0,
                'last_updated': time.time()
            })

            metrics['total_executions'] += 1
            if execution_data['success']:
                metrics['successful_executions'] += 1

            # Update average execution time
            current_avg = metrics['average_execution_time']
            new_time = execution_data['total_execution_time_ms']
            metrics['average_execution_time'] = (
                (current_avg * (metrics['total_executions'] - 1) + new_time) /
                metrics['total_executions']
            )

            metrics['last_updated'] = time.time()
            cache.set(cache_key, metrics, 3600)  # Cache for 1 hour

        except Exception as e:
            logger.error(f"Failed to record workflow metrics: {str(e)}")

    @classmethod
    def get_workflow_health_status(cls) -> Dict[str, Any]:
        """Get current workflow system health status"""
        try:
            # Get recent execution statistics
            recent_logs = PolicyWorkflowAuditLog.objects.filter(
                created_on__gte=timezone.now() - timedelta(hours=1)
            )

            total_executions = recent_logs.count()
            successful_executions = recent_logs.filter(success=True).count()

            success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0

            avg_execution_time = recent_logs.aggregate(
                avg_time=Avg('total_execution_time_ms')
            )['avg_time'] or 0

            return {
                'status': 'healthy' if success_rate >= 95 else 'degraded' if success_rate >= 80 else 'unhealthy',
                'success_rate': round(success_rate, 2),
                'total_executions_last_hour': total_executions,
                'average_execution_time_ms': round(avg_execution_time, 2),
                'last_updated': timezone.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get workflow health status: {str(e)}")
            return {
                'status': 'unknown',
                'error': str(e),
                'last_updated': timezone.now().isoformat()
            }
```

### Conclusion

This migration plan provides a comprehensive approach to moving the policy workflow system from frontend to backend execution. The implementation focuses on:

- **Reliability**: Robust error handling, retry logic, and caching
- **Performance**: Optimized database queries, intelligent caching, and efficient API design
- **Security**: Proper authentication, data protection, and audit logging
- **Maintainability**: Clean service architecture, comprehensive testing, and documentation
- **Scalability**: Designed to handle increased load and future enhancements
- **Monitoring**: Comprehensive metrics and health monitoring for operational excellence

The phased approach ensures minimal disruption to users while providing a more robust and maintainable system architecture. The detailed API specifications and implementation guidelines provide clear direction for the development team to execute this migration successfully.
